{"name": "@repo/model-transformer", "version": "0.0.0", "private": true, "files": ["dist"], "main": "./dist/es/index.js", "module": "./dist/es/index.js", "types": "./dist/es/index.d.ts", "exports": {".": {"import": {"types": "./dist/es/index.d.ts", "default": "./dist/es/index.js"}, "require": {"types": "./dist/cjs/index.d.cts", "default": "./dist/cjs/index.cjs"}}}, "scripts": {"build": "bunchee", "dev": "bunchee --watch", "check-types": "tsc src/index.ts --noEmit", "lint": "eslint src/ --max-warnings 0"}, "dependencies": {"@types/express": "^4.17.17", "@types/node": "^20.0.0", "express": "^4.18.2", "supertokens-node": "23.0.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "@repo/typescript-config": "workspace:*", "@repo/abstract": "workspace:*", "http-status-codes": "2.3.0", "reflect-metadata": "0.2.2"}, "devDependencies": {"bunchee": "^6.4.0", "eslint": "^9.31.0", "jest": "^29.7.0", "typescript": "5.8.2", "jest-environment-jsdom": "^29.7.0", "@repo/typescript-config": "workspace:*", "@types/node": "^20.0.0"}}