import { PrismaClient, User } from "@repo/database";
import { BaseRepository } from "@repo/abstract";

export class UserRepository extends BaseRepository<User> {

    constructor(prisma: PrismaClient) {
        super(prisma, "user");
    }

    public async findByEmail(email: string): Promise<User | null> {
        return this.prisma.user.findUnique({
            where: { emailId: email }
        });
    }

    public async findById(id: string): Promise<User | null> {
        return this.prisma.user.findUnique({
            where: { id },
            include: {
                tenant: true,
            },
        });
    }

    public async findAll(): Promise<User[]> {
        return this.prisma.user.findMany({
            include: {
                tenant: true,
            },
        });
    }

    public async create(userData: Omit<User, 'id' | 'createdAt' | 'updatedAt'>): Promise<User> {
        return this.prisma.user.create({
            data: userData,
        });
    }

    public async update(id: string, userData: Partial<User>): Promise<User> {
        return this.prisma.user.update({
            where: { id },
            data: userData,
        });
    }

    public async delete(id: string): Promise<User> {
        return this.prisma.user.delete({
            where: { id },
        });
    }

    public async block(userId: string[]): Promise<void> {
        return this.prisma.$transaction(async (tx: PrismaClient) => {
            await tx.user.updateMany({
                where: {
                    id: { in: userId },
                    tenantId: this.getTenantId(),
                },
                data: { isActive: false },
            });
        });
    }

    public async activate(userId: string): Promise<User> {
        return this.prisma.user.update({
            where: { id: userId },
            data: { isActive: true },
        });
    }

    public async deactivate(userId: string): Promise<User> {
        return this.prisma.user.update({
            where: { id: userId },
            data: { isActive: false },
        });
    }

    public async findByTenant(tenantId: string): Promise<User[]> {
        return this.prisma.user.findMany({
            where: { tenantId },
            include: {
                tenant: true,
            },
        });
    }

    public async countByTenant(tenantId: string): Promise<number> {
        return this.prisma.user.count({
            where: { tenantId },
        });
    }
}