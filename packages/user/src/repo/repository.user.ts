import  {  PrismaClient, User } from "@repo/database";


import {BaseRepository} from "@repo/abstract";



class UserRepository extends BaseRepository<any> {
    
    constructor(prisma: PrismaClient) {
        super(prisma, "user");
    }

    public async block(userId: string[]){
        return this.prisma.$transaction(async (tx:PrismaClient) => {
            await tx.user.updateMany({
                where: {
                    id: {in: userId},
                    tenantId: this.getTenantId(),
                },
                data: {lastName: "stupid-me"},
            });
        });
    }
}