import { BaseService, Service, ServiceError } from "@repo/abstract";
import { getPrisma } from "@repo/database";
import { UpdateUserDto } from "../model/model.user.update";

interface CreateUserPayload {
  firstName: string;
  lastName: string;
  displayName: string;
  emailId: string;
  role: string;
  tenantId?: string;
}



export class UserService extends BaseService<any, any> {
  private prisma = getPrisma();

  constructor() {
    super("UserService");
  }

  // Helper function to generate display name
  private generateDisplayName(firstName: string, lastName: string): string {
    return `${firstName.trim()} ${lastName.trim()}`.trim();
  }

  public async createUser(params: Service.Params<CreateUserPayload>) {
    return this.execute(params, async (params) => {
      const { payload } = params;

      // Basic validation
      if (!payload.firstName || !payload.lastName || !payload.emailId || !payload.role) {
        throw new ServiceError(
          "firstName, lastName, emailId, and role are required",
          "VALIDATION_ERROR",
          400
        );
      }

      // Check if user already exists
      const existingUser = await this.prisma.user.findUnique({
        where: { emailId: payload.emailId }
      });

      if (existingUser) {
        throw new ServiceError(
          `User with email ${payload.emailId} already exists`,
          "USER_EXISTS",
          409
        );
      }

      // Create user
      try {
        const user = await this.prisma.user.create({
          data: {
            firstName: payload.firstName,
            lastName: payload.lastName,
            displayName: payload.displayName || this.generateDisplayName(payload.firstName, payload.lastName),
            emailId: payload.emailId,
            role: payload.role,
            tenantId: payload.tenantId || 'default-tenant',
            isActive: true,
          },
        });

        return user;
      } catch (error: any) {
        if (error.code === "P2002") {
          throw new ServiceError(
            "Email already exists",
            "EMAIL_EXISTS",
            400
          );
        }
        throw new ServiceError(
          "Failed to create user",
          "CREATE_USER_ERROR",
          500,
          error
        );
      }
    });
  }

  public async getUserById(params: Service.Params<{ id: string }>) {
    return this.execute(params, async (params) => {
      const { payload } = params;

      if (!payload.id) {
        throw new ServiceError(
          "User ID is required",
          "VALIDATION_ERROR",
          400
        );
      }

      const user = await this.prisma.user.findUnique({
        where: { id: payload.id },
        include: {
          tenant: true,
        },
      });

      if (!user) {
        throw new ServiceError(
          "User not found",
          "USER_NOT_FOUND",
          404
        );
      }

      return user;
    });
  }

  public async getAllUsers(params: Service.Params<any>) {
    return this.execute(params, async () => {
      const users = await this.prisma.user.findMany({
        include: {
          tenant: true,
        },
      });

      return users;
    });
  }

  public async updateUser(params: Service.Params<{ id: string; data: UpdateUserDto }>) {
    return this.execute(params, async (params) => {
      const { payload } = params;

      if (!payload.id) {
        throw new ServiceError(
          "User ID is required",
          "VALIDATION_ERROR",
          400
        );
      }

      // If firstName or lastName is updated but displayName is not provided, regenerate it
      if ((payload.data.firstName || payload.data.lastName) && !payload.data.displayName) {
        const currentUser = await this.prisma.user.findUnique({
          where: { id: payload.id },
        });

        if (currentUser) {
          const newFirstName = payload.data.firstName || currentUser.firstName;
          const newLastName = payload.data.lastName || currentUser.lastName;
          payload.data.displayName = this.generateDisplayName(newFirstName, newLastName);
        }
      }

      try {
        const user = await this.prisma.user.update({
          where: { id: payload.id },
          data: payload.data,
        });

        return user;
      } catch (error: any) {
        if (error.code === "P2025") {
          throw new ServiceError(
            "User not found",
            "USER_NOT_FOUND",
            404
          );
        } else if (error.code === "P2002") {
          throw new ServiceError(
            "Email already exists",
            "EMAIL_EXISTS",
            400
          );
        }
        throw new ServiceError(
          "Failed to update user",
          "UPDATE_USER_ERROR",
          500,
          error
        );
      }
    });
  }

  public async deleteUser(params: Service.Params<{ id: string }>) {
    return this.execute(params, async (params) => {
      const { payload } = params;

      if (!payload.id) {
        throw new ServiceError(
          "User ID is required",
          "VALIDATION_ERROR",
          400
        );
      }

      try {
        const user = await this.prisma.user.delete({
          where: { id: payload.id },
        });

        return user;
      } catch (error: any) {
        if (error.code === "P2025") {
          throw new ServiceError(
            "User not found",
            "USER_NOT_FOUND",
            404
          );
        }
        throw new ServiceError(
          "Failed to delete user",
          "DELETE_USER_ERROR",
          500,
          error
        );
      }
    });
  }
}
