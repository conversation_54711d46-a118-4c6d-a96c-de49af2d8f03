import { BaseService, Service, ServiceError } from "@repo/abstract";


interface User {
  id?: number;
  name: string;
  email: string;
  password: string;
}



export class UserService extends BaseService<User, User> {
  constructor() {
    super("UserService");
  }


  public async createUser(params: Service.Params<User>) {
    return this.execute(params, async (params) => {
      const { payload } = params;

      if (!payload.email || !payload.password || !payload.name) {
        throw new ServiceError(
          "Name, email, and password are required",
          "VALIDATION_ERROR",
          400
        );
      }

      const existingUser = await this.userRepo.findByEmail(payload.email);
      if (existingUser) {
        throw new ServiceError(
          `User with email ${payload.email} already exists`,
          "USER_EXISTS",
          409
        );
      }

      // Optionally hash password here before saving
      const newUser = await this.userRepo.create(payload);

      return newUser;
    });
  }
}
