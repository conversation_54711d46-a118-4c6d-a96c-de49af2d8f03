import { BaseController } from "@repo/abstract";
import { type Request, type Response, Router } from "express";
import { UserService } from "../service/service.user";
import { Specification } from "@repo/model-transformer";
import { CreateUserDto } from "../model/model.user.create";
import { UpdateUserDto } from "../model/model.user.update";
export class UserController extends BaseController {
    private readonly router: Router;
    private service: UserService;

    constructor() {
        super();
        this.router = Router();
        this.service = new UserService();
    }

    @Specification({ param_blueprint: CreateUserDto }, { validate: true })
    override async create(req: Request, res: Response): Promise<Response> {
        try {
            const result = await this.service.createUser({
                requestId: req.headers['x-request-id'] as string || 'default-request-id',
                payload: req.body,
                context: {},
                metadata: {}
            });

            return res.status(201).json(result);
        } catch (error: any) {
            const statusCode = error.status || 500;
            return res.status(statusCode).json({ error: error.message });
        }
    }

    override async get(req: Request, res: Response): Promise<Response> {
        try {
            const result = await this.service.getUserById({
                requestId: req.headers['x-request-id'] as string || 'default-request-id',
                payload: { id: req.params.id as string },
                context: {},
                metadata: {}
            });

            return res.json(result);
        } catch (error: any) {
            const statusCode = error.status || 500;
            return res.status(statusCode).json({ error: error.message });
        }
    }

    override async find(req: Request, res: Response): Promise<Response> {
        try {
            const result = await this.service.getAllUsers({
                requestId: req.headers['x-request-id'] as string || 'default-request-id',
                payload: {},
                context: {},
                metadata: {}
            });

            return res.json(result);
        } catch (error: any) {
            const statusCode = error.status || 500;
            return res.status(statusCode).json({ error: error.message });
        }
    }

    @Specification({ param_blueprint: UpdateUserDto }, { validate: true })
    override async update(req: Request, res: Response): Promise<Response> {
        try {
            const result = await this.service.updateUser({
                requestId: req.headers['x-request-id'] as string || 'default-request-id',
                payload: { id: req.params.id as string, data: req.body },
                context: {},
                metadata: {}
            });

            return res.json(result);
        } catch (error: any) {
            const statusCode = error.status || 500;
            return res.status(statusCode).json({ error: error.message });
        }
    }

    override async delete(req: Request, res: Response): Promise<Response> {
        try {
            const result = await this.service.deleteUser({
                requestId: req.headers['x-request-id'] as string || 'default-request-id',
                payload: { id: req.params.id as string },
                context: {},
                metadata: {}
            });

            return res.json({ message: "User deleted successfully", user: result });
        } catch (error: any) {
            const statusCode = error.status || 500;
            return res.status(statusCode).json({ error: error.message });
        }
    }

    getRouter(): Router {
        this.router.post("/", this.create.bind(this));
        this.router.get("/:id", this.get.bind(this));
        this.router.get("/", this.find.bind(this));
        this.router.put("/:id", this.update.bind(this));
        this.router.delete("/:id", this.delete.bind(this));

        return this.router;
    }

}
