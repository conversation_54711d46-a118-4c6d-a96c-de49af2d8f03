import { BaseController } from "@repo/abstract";
import { type Request, type Response, Router } from "express";
import { UserService } from "../service/service.user";
import { Specification } from "@repo/model-transformer";
export class UserController extends BaseController {
    private readonly router: Router;
    private service: UserService;

    constructor() {
        super();
        this.router = Router();
        this.service = new UserService();
    }

    override async create(req: Request, res: Response): Promise<Response> {
        throw new Error("Method not implemented.");
    }

    override async get(req: Request, res: Response): Promise<Response> {
        throw new Error("Method not implemented.");
    }

    override async find(req: Request, res: Response): Promise<Response> {
        throw new Error("Method not implemented.");
    }

    @Specification()
    override async update(req: Request, res: Response): Promise<Response> {
        this.service.createUsers();
        throw new Error("Method not implemented.");

    }

    override async delete(req: Request, res: Response): Promise<Response> {
        throw new Error("Method not implemented.");
    }

    getRouter(): Router {
        this.router.post("/", this.create.bind(this));
        this.router.get("/:id", this.get.bind(this));
        this.router.get("/", this.find.bind(this));
        this.router.put("/:id", this.update.bind(this));
        this.router.delete("/:id", this.delete.bind(this));

        return this.router;
    }

}
