import { IsString, IsEmail, IsNotEmpty, Length, IsOptional } from '@repo/model-transformer';

export class UpdateUserDto {
    @IsString()
    @IsOptional()
    @IsNotEmpty({ message: 'First name is required' })
    @Length(2, 50, { message: 'First name must be between 2 and 50 characters' })
    firstName?: string;

    @IsString()
    @IsOptional()
    @IsNotEmpty({ message: 'Last name is required' })
    @Length(2, 50, { message: 'Last name must be between 2 and 50 characters' })
    lastName?: string;

    @IsString()
    @IsOptional()
    @IsNotEmpty({ message: 'Display name is required' })
    @Length(2, 100, { message: 'Display name must be between 2 and 100 characters' })
    displayName?: string;

    @IsEmail({}, { message: 'Email must be valid' })
    @IsOptional()
    @IsNotEmpty({ message: 'Email is required' })
    emailId?: string;

    @IsString()
    @IsOptional()
    @IsNotEmpty({ message: 'Role is required' })
    role?: string;
}
