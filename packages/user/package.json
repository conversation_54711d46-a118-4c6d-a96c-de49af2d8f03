{"name": "@repo/user", "version": "0.0.0", "private": true, "files": ["dist"], "main": "./dist/es/index.js", "module": "./dist/es/index.js", "types": "./dist/es/index.d.ts", "exports": {".": {"import": {"types": "./dist/es/index.d.ts", "default": "./dist/es/index.js"}, "require": {"types": "./dist/cjs/index.d.cts", "default": "./dist/cjs/index.cjs"}}}, "scripts": {"build": "bunchee", "dev": "bunchee --watch", "check-types": "tsc src/index.ts --noEmit", "lint": "eslint src/ --max-warnings 0"}, "dependencies": {"@repo/abstract": "workspace:*", "@repo/common": "workspace:*", "@types/node": "^20.0.0", "jest": "^29.7.0", "package.json": "^2.0.1", "ts-jest": "^29.4.0", "@repo/database": "workspace:*", "express": "5.1.0", "@repo/model-transformer": "workspace:*"}, "devDependencies": {"@repo/abstract": "workspace:*", "@repo/common": "workspace:*", "@repo/typescript-config": "workspace:*", "@types/node": "^20.0.0", "bunchee": "^6.4.0", "eslint": "^9.31.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "typescript": "5.8.2", "@types/express": "5.0.3"}, "prisma": {"schema": "database/prisma/schema.prisma"}}