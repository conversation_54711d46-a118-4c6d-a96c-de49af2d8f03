export class ServiceError extends Error {
  public code: string;
  public status: number;
  public originalError?: Error;

  constructor(message: string, code: string, status: number = 500, originalError?: Error) {
    super(message);
    this.name = "ServiceError";
    this.code = code;
    this.status = status;
    this.originalError = originalError;

    // Maintain proper stack trace (only in V8 environments)
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, ServiceError);
    }
  }
}


export interface ServiceHooks<TPayload = any, TResult = any> {
  readonly preExecute?: Array<Service.PreExecuteHook<TPayload>>;
  readonly postExecute?: Array<Service.PostExecuteHook<TPayload, TResult>>;
  readonly onError?: Array<Service.ErrorHook>;
}

export abstract class BaseService<TPayload = any, TResult = any> {
  constructor(protected readonly serviceName: string = "BaseService") {}

  private async executePreHooks<T>(
    hooks: Array<Service.PreExecuteHook<T>>,
    params: Service.Params<T>
  ): Promise<void> {
    if (!hooks?.length) return;

    try {
      await Promise.all(
        hooks.map(async (hook, index) => {
          try {
            await hook(params);
          } catch (hookError: any) {
            throw new ServiceError(
              `Pre-execution hook ${index} failed: ${hookError.message}`,
              "PRE_HOOK_ERROR",
              500,
              hookError
            );
          }
        })
      );
    } catch (error: any) {
      throw error instanceof ServiceError
        ? error
        : new ServiceError("Pre-execution hooks failed", "PRE_HOOKS_ERROR", 500, error);
    }
  }

  private async executePostHooks<T>(
    hooks: Array<Service.PostExecuteHook<T, TResult>>,
    params: Service.Params<T>,
    response: Service.Response<TResult>
  ): Promise<void> {
    if (!hooks?.length) return;

    const hookPromises = hooks.map(async (hook, index) => {
      try {
        await hook(params, response);
      } catch (hookError: any) {
        console.error(`Post-execution hook ${index} failed:`, {
          error: hookError.message,
          executionId: params.context.executionId,
          serviceName: this.serviceName,
        });
      }
    });

    Promise.allSettled(hookPromises).catch(console.error);
  }

  private async executeErrorHooks(
    hooks: Array<Service.ErrorHook>,
    error: ServiceError,
    params: Service.Params
  ): Promise<void> {
    if (!hooks?.length) return;

    const hookPromises = hooks.map(async (hook, index) => {
      try {
        await hook(error, params);
      } catch (hookError: any) {
        console.error(`Error hook ${index} failed:`, {
          error: hookError.message,
          originalError: error.message,
        });
      }
    });

    Promise.allSettled(hookPromises).catch(console.error);
  }

  private error(
    error: ServiceError,
    executionTime: number,
    context?: Record<string, any>
  ): Service.ErrorResponse<TResult> {
    return Object.freeze({
      success: false,
      code: error.code || "SERVICE_ERROR",
      message: error.message || "An unexpected error occurred",
      error,
      executionTime,
      context,
    });
  }

  private success<TResult>(
    result: TResult,
    message = "OK",
    code = "200",
    executionTime: number = 0,
    context: Record<string, any> = {}
  ): Service.Response<TResult> {
    return {
      success: true,
      message,
      result,
      code,
      executionTime,
      context,
    };
  }

  protected async execute(
    params: Service.Params<TPayload>,
    operation: Service.Operation<TPayload, TResult>
  ): Promise<Service.Response<TResult> | Service.ErrorResponse<TResult>> {
    const startTime = Date.now();
    let response;
    try {
      // Execute pre-hooks
      await this.executePreHooks(params.preHooks || [], params);

      const rawResult = await operation(params);

      response = this.success(rawResult, "OK", "200", Date.now() - startTime);
      this.executePostHooks(params.postHooks || [], params, response);

      return response;
    } catch (error: any) {
      const executionTime = Date.now() - startTime;
      const serviceError =
        error instanceof ServiceError
          ? error
          : new ServiceError(error.message, "UNKNOWN_ERROR", 500, error);

      response = this.error(serviceError, executionTime);

      this.executeErrorHooks(params.onError || [], serviceError, params);

      return response;
    }
  }
}



export namespace Service {
  export interface Response<TResult = any> {
    readonly success: boolean;
    readonly code: string;
    readonly message: string;
    readonly result?: TResult;
    readonly error?: any; // ServiceError from @repo/error
    readonly executionTime: number;
    readonly context: Record<string, any>;
  }

  export interface Params<TPayload = any> {
    readonly preHooks?: Array<PreExecuteHook<TPayload>>;
    readonly postHooks?: Array<PostExecuteHook<TPayload, any>>;
    readonly onError?: Array<ErrorHook>;
    readonly requestId: string;
    readonly userId?: string;
    readonly tenantId?: string;
    readonly payload: TPayload;
    readonly metadata?: Record<string, any>;
    readonly context: Record<string, any>;
  }

  export interface ErrorResponse<TResult = any> {
    readonly success: false;
    readonly code: string;
    readonly message: string;
    readonly error: any; // ServiceError from @repo/error
    readonly executionTime: number;
    readonly result?: TResult; // optional, usually absent,
    context?: Record<string, any>;
  }

  export type PreExecuteHook<TPayload> = (params: Service.Params<TPayload>) => Promise<void> | void;

  export type PostExecuteHook<TPayload, TResult> = (
    params: Service.Params<TPayload>,
    response: Service.Response<TResult>
  ) => Promise<void> | void;

  export type ErrorHook = (
    error: any, // ServiceError from @repo/error
    request: Params<any>
  ) => Promise<void> | void;

  export type Operation<TPayload, TResult> = (params: Service.Params<TPayload>) => Promise<TResult>;
}
