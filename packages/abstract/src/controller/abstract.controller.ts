import { Request, Response, Router } from 'express';

export abstract class BaseController {

  abstract create(req: Request, res: Response): Promise<Response>;
  abstract get(req: Request, res: Response): Promise<Response>;
  abstract find(req: Request, res: Response): Promise<Response>;
  abstract update(req: Request, res: Response): Promise<Response>;
  abstract delete(req: Request, res: Response): Promise<Response>;

  abstract getRouter(): Router;
}
