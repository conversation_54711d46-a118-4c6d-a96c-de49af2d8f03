import { PrismaClient } from "@prisma/client";
import { ApplicationContext } from "@repo/common";

export class BaseRepository<T> {
  protected prisma: PrismaClient;
  protected entity: keyof PrismaClient;

  constructor(prisma: PrismaClient, entity: keyof PrismaClient) {
    this.prisma = prisma;
    this.entity = entity;
  }

  protected getTenantId(): string {
    const ctx = ApplicationContext.require();
    if (!ctx.user?.tenantId) {
      throw new Error('No tenant ID found in context');
    }
    return ctx.user.tenantId;
  }



  public readonly findMany = async (args: Record<string, any> = {}): Promise<T[]> => {
    return (this.prisma[this.entity] as any).findMany({
      where: { tenantId: this.getTenantId, ...args.where },
      ...args,
    });
  };

  public readonly findUnique = async (args: Record<string, any>): Promise<T | null> => {
    return (this.prisma[this.entity] as any).findUnique({
      where: { ...args.where, tenantId: this.getTenantId },
      ...args,
    });
  };

  public readonly create = async (data: Record<string, any>): Promise<T> => {
    return (this.prisma[this.entity] as any).create({
      data: { ...data, tenantId: this.getTenantId },
    });
  };

  public readonly update = async (args: Record<string, any>): Promise<T> => {
    return (this.prisma[this.entity] as any).update({
      where: { ...args.where, tenantId: this.getTenantId },
      data: args.data,
    });
  };

  public readonly delete = async (args: Record<string, any>): Promise<T> => {
    return (this.prisma[this.entity] as any).delete({
      where: { ...args.where, tenantId: this.getTenantId },
    });
  };
}
