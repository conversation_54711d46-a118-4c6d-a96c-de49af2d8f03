{"name": "@repo/abstract", "version": "0.0.0", "private": true, "files": ["dist"], "main": "./dist/es/index.js", "module": "./dist/es/index.js", "types": "./dist/es/index.d.ts", "exports": {".": {"import": {"types": "./dist/es/index.d.ts", "default": "./dist/es/index.js"}, "require": {"types": "./dist/cjs/index.d.cts", "default": "./dist/cjs/index.cjs"}}}, "scripts": {"build": "bunchee", "dev": "bunchee --watch", "check-types": "tsc src/index.ts --noEmit", "lint": "eslint src/ --max-warnings 0"}, "dependencies": {"@prisma/client": "^5.20.0", "pg": "^8.16.3", "prisma": "^5.20.0", "express": "5.1.0", "@repo/common": "workspace:*"}, "devDependencies": {"@types/express": "5.0.3", "@repo/eslint-config": "*", "@repo/typescript-config": "*", "@types/node": "^22.15.3", "@types/pg": "^8.15.5", "eslint": "^9.31.0", "tsup": "^8.5.0", "bunchee": "^6.4.0", "typescript": "5.8.2"}}