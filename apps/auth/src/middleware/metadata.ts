import UserMetadata from "supertokens-node/recipe/usermetadata";
import { ApplicationContext } from "@repo/common";
export const metadataMiddleware = async (req: any, res: any, next: any) => {
  try {
    const userId = req.session!.getUserId();
    const metadata = await UserMetadata.getUserMetadata(userId);
    req.metadata = metadata.metadata;
    
    const ctx = {
      user: metadata.metadata
    };

    delete req.headers["authorization"];
    ApplicationContext.run(ctx, () => next());

  } catch (err) {
    console.error("metadata middleware error:", err);
    res.status(500).json({ error: "Failed to fetch metadata" });
  }
};
