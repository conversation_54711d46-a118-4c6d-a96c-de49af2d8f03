import supertokens from "supertokens-node";
import EmailVerification from "supertokens-node/recipe/emailverification";
import Session from "supertokens-node/recipe/session";
import EmailPassword from "supertokens-node/recipe/emailpassword";
import { middleware, errorHandler } from "supertokens-node/framework/express";
import { SMTPService } from "supertokens-node/recipe/emailpassword/emaildelivery";
import UserMetadata from "supertokens-node/recipe/usermetadata";
import { StaffUserService } from "../services/StaffUserService";

export interface SuperTokensConfig {
  connectionURI: string;
  appInfo: {
    appName: string;
    apiDomain: string;
    websiteDomain: string;
    apiBasePath?: string;
    websiteBasePath?: string;
  };
}
export class SuperTokensService {
  private config: SuperTokensConfig;
  private initialized = false;

  constructor(config: SuperTokensConfig) {
    this.config = config;
  }

  /**
   * Initialize SuperTokens with the provided configuration
   */
  init(): void {
    supertokens.init({
      framework: "express",
      supertokens: {
        connectionURI: process.env.SUPERTOKENS_CONNECTION_URI || "http://localhost:3567",
      },
      appInfo: {
        appName: "CadetLabs",
        apiDomain: "http://localhost:3005", // Auth app domain
        websiteDomain: "http://localhost:3000", // Frontend domain
        apiBasePath: "/auth",
        websiteBasePath: "/auth",
      },
      recipeList: [
        EmailVerification.init({
          mode: "REQUIRED", // or "OPTIONAL"
        }),
        EmailPassword.init({
          override: {
            apis: (originalImplementation) => {
              return {
                ...originalImplementation,
                signInPOST: async (input) => {
                  console.log("Signin input:", input);
                  const email = input.formFields.find((f) => f.id === "email")?.value;
                  console.log("Signin input email:", email);

                  const isActive = await StaffUserService.checkEmailIsActive(email as string);
                  console.log("Signin input email is active:", isActive);
                  if (!isActive) {
                    return {
                      status: "CUSTOM_ERROR",
                      message: "User is deactivated. Please contact support.",
                    };
                  }

                  return await originalImplementation.signInPOST(input);
                },
                signUpPOST: undefined,
              };
            },
          },
        }),
        UserMetadata.init(),
        Session.init({
          cookieSecure: process.env.NODE_ENV === "production",
          cookieSameSite: "lax",
          exposeAccessTokenToFrontendInCookieBasedAuth: true,
        }),
      ],
    });

    this.initialized = true;
  }
  getMiddleware() {
    if (!this.initialized) {
      throw new Error("SuperTokens not initialized. Call init() first.");
    }
    return middleware();
  }
}
export function createDefaultSuperTokensService(): SuperTokensService {
  return new SuperTokensService({
    connectionURI: process.env.SUPERTOKENS_CONNECTION_URI || "http://localhost:3567",
    appInfo: {
      appName: "CadetLabs",
      apiDomain: "http://localhost:3005", // Auth app domain
      websiteDomain: "http://localhost:3000", // Frontend domain
      apiBasePath: "/auth",
      websiteBasePath: "/auth",
    },
  });
}
